version: '3.8'

services:
  db:
    image: postgres:latest
    environment:
      POSTGRES_DB: star_wars_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  migracoes:
    build: .
    volumes:
      - ./:/app
    working_dir: /app/src/migracoes
    command: alembic -c alembic.ini upgrade head
    depends_on:
      db:
        condition: service_healthy
    environment:
      DATABASE_URL: **************************************/star_wars_db



volumes:
  postgres_data:
