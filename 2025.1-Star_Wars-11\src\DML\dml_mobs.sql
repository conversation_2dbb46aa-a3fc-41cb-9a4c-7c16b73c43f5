INSERT INTO MOB (tipo_mob, nivel_ameaca)
VALUES 
    ('Stormtrooper', 1),
    ('Dark Trooper', 2),
    ('<PERSON>nco<PERSON>', 3);

INSERT INTO Inimigo (vida_base, nivel, dano_base, pontos_escudo, creditos, tipo_mob, planeta_origem)
VALUES 
    (100, 1, 20, 0, 50, '<PERSON>trooper', '<PERSON><PERSON><PERSON><PERSON>'),
    (200, 5, 40, 50, 150, 'Dark Trooper', 'Coruscant'),
    (500, 10, 100, 100, 500, 'Rancor', '<PERSON><PERSON><PERSON><PERSON>');

INSERT INTO Normal (tipo_mob, Formacao_Tatica, Patrulha, Ataque_Coordenado)
VALUES ('Stormtrooper', true, true, true);

INSERT INTO Elite (tipo_mob, Armadura_Reforçada, Ataque_Especial, Regeneracao)
VALUES ('Dark Trooper', true, true, false);

INSERT INTO Boss (tipo_mob, Arsenal, Habilidade_Unica, Invocacao_Aliados)
VALUES ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> e <PERSON>sas', true, true);

INSERT INTO Inventario_IA (id_mob, item, quantidade, raridade)
VALUES 
    (1, 'Blaster E-11', 1, 'Comum'),
    (2, 'Dark Trooper Armor', 1, 'Raro'),
    (3, 'Garra do Rancor', 1, '<PERSON>pico');