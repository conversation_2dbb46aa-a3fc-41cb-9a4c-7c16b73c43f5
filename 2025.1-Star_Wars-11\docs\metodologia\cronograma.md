# 📅 Cronograma do Projeto – Banco de Dados (MUD Star Wars)

Este projeto é um jogo estilo **MUD (Multi-User Dungeon)** textual, ambientado no universo de **Star Wars**. Os jogadores interagem em tempo real, completam missões, exploram planetas e gerenciam personagens, naves e itens — tudo estruturado sobre um banco de dados relacional.

---

## ✅ Etapas do Projeto

### 🧱 Fase 1 – Modelagem e Projeto de Banco de Dados
> Criação da estrutura conceitual e lógica do banco de dados.

- [ ] Definição das entidades principais: Jogador, Nave, Missão, Item, Planeta, Classe
- [ ] Construção do Diagrama Entidade-Relacionamento (DER)
- [ ] Mapeamento para o Modelo Relacional
- [ ] Desenvolvimento do Dicionário de Dados
- [ ] Aplicação de regras de integridade e integridade referencial
- [ ] Normalização do modelo até a 4FN (Forma Normal)

### 🧩 Fase 2 – Implementação em SQL
> Criação física do banco, inserção de dados e consultas.

- [ ] Criação das tabelas e constraints no SGBD
- [ ] Inserção de dados iniciais (planetas, classes, jogadores, itens, naves)
- [ ] Desenvolvimento de consultas SQL (ex: progresso em missões, inventário de jogadores, interações no planeta)
- [ ] Criação de views auxiliares se necessário
- [ ] Tradução de consultas para Álgebra Relacional (documentação)

### ⚙️ Fase 3 – Regras de Negócio e Funcionalidades Avançadas
> Automatizações e segurança do banco de dados.

- [ ] Implementação de triggers (ex: log de ações, controle de missões)
- [ ] Criação de stored procedures para operações recorrentes (ex: aceitar missão, finalizar batalha)
- [ ] Criação de views para consultas recorrentes
- [ ] Definição de controle de acesso e privilégios
- [ ] Simulação de concorrência e transações seguras

---

## 🚀 Entregas

- ✅ DER e Modelo Relacional
- ✅ Dicionário de Dados
- ✅ Scripts SQL (DDL, DML, DQL)
- ✅ Consultas com Álgebra Relacional
- ✅ Triggers, Procedures, Views e Segurança

---

## 🛠 Tecnologias e Ferramentas

- PostgreSQL
- GitHub (com commits individuais e frequentes)
- Ferramentas de modelagem: dbdiagram.io, Lucidchart ou similar
- Documentação em Markdown

---

> Este cronograma é uma referência para acompanhamento do desenvolvimento e organização do projeto. Que a força esteja com você! 🌌
