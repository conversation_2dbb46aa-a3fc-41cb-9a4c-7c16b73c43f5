-- Consultas relacionadas ao sistema de combate

-- Listar todos os inimigos disponíveis por planeta
SELECT 
    p.nome_planeta,
    i.tipo_mob,
    i.nivel,
    i.vida_base,
    i.dano_base,
    pip.probabilidade_spawn,
    pip.nivel_minimo,
    pip.nivel_maximo
FROM Planeta p
JOIN Planeta_Inimigo_Permitido pip ON p.nome_planeta = pip.nome_planeta
JOIN Inimigo i ON pip.tipo_mob = i.tipo_mob AND pip.nome_planeta = i.planeta_origem
ORDER BY p.nome_planeta, i.nivel;

-- Estatísticas de combate por jogador
SELECT 
    p.id_player,
    p.nome_classe,
    p.level,
    COUNT(c.id_combate) as total_combates,
    COUNT(CASE WHEN c.resultado = 'Vitoria' THEN 1 END) as vitorias,
    COUNT(CASE WHEN c.resultado = 'Derrota' THEN 1 END) as derrotas,
    ROUND(
        COUNT(CASE WHEN c.resultado = 'Vitoria' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(c.id_combate), 0), 2
    ) as taxa_vitoria,
    SUM(c.xp_ganho) as total_xp_combate,
    SUM(c.creditos_ganho) as total_creditos_combate
FROM Personagem p
LEFT JOIN Combate c ON p.id_player = c.id_player
GROUP BY p.id_player, p.nome_classe, p.level
ORDER BY total_combates DESC;

-- Inimigos mais enfrentados
SELECT 
    i.tipo_mob,
    i.planeta_origem,
    COUNT(c.id_combate) as vezes_enfrentado,
    COUNT(CASE WHEN c.resultado = 'Vitoria' THEN 1 END) as derrotas_inimigo,
    COUNT(CASE WHEN c.resultado = 'Derrota' THEN 1 END) as vitorias_inimigo,
    AVG(c.dano_causado) as dano_medio_recebido,
    AVG(c.dano_recebido) as dano_medio_causado
FROM Inimigo i
JOIN Combate c ON i.id_mob = c.id_mob
GROUP BY i.tipo_mob, i.planeta_origem
ORDER BY vezes_enfrentado DESC;

-- Combates recentes (últimos 10)
SELECT 
    c.data_combate,
    p.nome_classe as jogador_classe,
    p.level as jogador_level,
    i.tipo_mob as inimigo_tipo,
    i.nivel as inimigo_nivel,
    i.planeta_origem,
    c.resultado,
    c.xp_ganho,
    c.creditos_ganho
FROM Combate c
JOIN Personagem p ON c.id_player = p.id_player
JOIN Inimigo i ON c.id_mob = i.id_mob
ORDER BY c.data_combate DESC
LIMIT 10;

-- Planetas mais perigosos (maior taxa de derrota)
SELECT 
    i.planeta_origem,
    COUNT(c.id_combate) as total_combates,
    COUNT(CASE WHEN c.resultado = 'Derrota' THEN 1 END) as derrotas_jogadores,
    ROUND(
        COUNT(CASE WHEN c.resultado = 'Derrota' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(c.id_combate), 0), 2
    ) as taxa_derrota_jogadores
FROM Inimigo i
JOIN Combate c ON i.id_mob = c.id_mob
GROUP BY i.planeta_origem
HAVING COUNT(c.id_combate) >= 5  -- Apenas planetas com pelo menos 5 combates
ORDER BY taxa_derrota_jogadores DESC;

-- Verificar configuração de inimigos por planeta
SELECT 
    pip.nome_planeta,
    COUNT(pip.tipo_mob) as tipos_inimigos_permitidos,
    STRING_AGG(pip.tipo_mob, ', ') as inimigos_disponiveis,
    AVG(pip.probabilidade_spawn) as probabilidade_media
FROM Planeta_Inimigo_Permitido pip
GROUP BY pip.nome_planeta
ORDER BY tipos_inimigos_permitidos DESC;

-- Função de teste para gerar inimigo (exemplo de uso)
-- SELECT * FROM gerar_inimigo_aleatorio(1);

-- Função de teste para calcular combate (exemplo de uso)
-- SELECT * FROM calcular_combate(1, 1);

-- Função de teste para histórico (exemplo de uso)
-- SELECT * FROM listar_historico_combates(1);
