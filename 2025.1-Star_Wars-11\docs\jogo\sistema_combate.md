# Sistema de Combate - Star Wars MUD

## 📋 Visão Geral

O sistema de combate implementa batalhas entre jogadores e inimigos baseado em regras específicas por planeta, utilizando lógica completamente implementada no banco de dados.

## 🎯 Características Principais

### ✅ Controle por Planeta
- **Inimigos específicos** por planeta baseado na tabela `Planeta_Inimigo_Permitido`
- **Probabilidade de spawn** configurável por tipo de inimigo
- **Níveis apropriados** baseados no nível do jogador

### ✅ Lógica no Banco de Dados
- **Geração de inimigos** via função `gerar_inimigo_aleatorio()`
- **Cálculo de combate** via função `calcular_combate()`
- **Validação automática** via triggers e constraints

## 🗂️ Estrutura de Tabelas

### Planeta_Inimigo_Permitido
```sql
nome_planeta VARCHAR(20)     -- Planeta onde o inimigo pode aparecer
tipo_mob VARCHAR(22)         -- Tipo do inimigo
probabilidade_spawn DECIMAL  -- Chance de aparecer (0.0 a 1.0)
nivel_minimo INT            -- Nível mínimo do inimigo
nivel_maximo INT            -- Nível máximo do inimigo
```

### Combate
```sql
id_combate INT              -- ID único do combate
id_player INT               -- Jogador participante
id_mob INT                  -- Inimigo enfrentado
data_combate TIMESTAMP      -- Quando ocorreu
resultado VARCHAR(20)       -- 'Vitoria', 'Derrota', 'Fuga'
xp_ganho INT               -- Experiência obtida
creditos_ganho INT         -- Créditos obtidos
dano_causado INT           -- Dano total causado
dano_recebido INT          -- Dano total recebido
```

## ⚔️ Mecânicas de Combate

### Geração de Inimigos
1. **Baseado no planeta** do jogador atual
2. **Nível apropriado**: ±3 níveis do jogador
3. **Probabilidade de spawn** respeitada
4. **Validação automática** via constraints

### Cálculo de Batalha
1. **Combate por turnos** simulado
2. **Jogador ataca primeiro**
3. **Inimigo contra-ataca** se sobreviver
4. **Continua até um morrer**

### Sistema de Recompensas
- **XP**: `nivel_inimigo * 100`
- **Créditos**: Valor definido no inimigo
- **Atualização automática** do personagem

## 🌍 Configuração por Planeta

### Tatooine (Desértico)
- **Stormtrooper**: 70% chance, níveis 1-5
- **Rancor**: 30% chance, níveis 8-15

### Coruscant (Imperial)
- **Stormtrooper**: 80% chance, níveis 1-8
- **Dark Trooper**: 60% chance, níveis 5-12

### Naboo (Pacífico)
- **Stormtrooper**: 40% chance, níveis 1-4

### Kashyyyk (Selvagem)
- **Stormtrooper**: 50% chance, níveis 2-6

### Hoth (Militar)
- **Stormtrooper**: 60% chance, níveis 3-7
- **Dark Trooper**: 40% chance, níveis 6-10

## 🎮 Como Usar no Jogo

### Comandos Disponíveis
```
4. combate - Sistema de combate
   1. Procurar inimigo
   2. Histórico de combates
   3. Voltar
```

### Fluxo de Combate
1. **Procurar inimigo** → Sistema gera inimigo aleatório
2. **Escolher ação** → Atacar ou Fugir
3. **Combate automático** → Resultado calculado
4. **Recompensas** → XP e créditos aplicados

## 🔧 Funções do Banco

### gerar_inimigo_aleatorio(jogador_id)
- Retorna inimigo apropriado para o planeta/nível do jogador
- Respeita probabilidades de spawn
- Filtra por níveis compatíveis

### calcular_combate(jogador_id, inimigo_id)
- Simula combate completo
- Atualiza estatísticas do jogador
- Registra histórico de combate
- Retorna resultado detalhado

### listar_historico_combates(jogador_id)
- Lista últimos 10 combates
- Mostra resultados e recompensas
- Ordenado por data (mais recente primeiro)

## 📊 Consultas Úteis

### Ver inimigos disponíveis por planeta
```sql
SELECT * FROM Planeta_Inimigo_Permitido 
WHERE nome_planeta = 'Tatooine';
```

### Estatísticas de combate do jogador
```sql
SELECT * FROM listar_historico_combates(1);
```

### Testar geração de inimigo
```sql
SELECT * FROM gerar_inimigo_aleatorio(1);
```

## 🛡️ Validações Implementadas

### Trigger de Validação
- **Inimigos só podem ser criados** em planetas permitidos
- **Erro automático** se tentar criar inimigo em planeta não configurado
- **Integridade referencial** garantida

### Constraints
- **Foreign Keys** para Planeta e MOB
- **Check constraints** para probabilidades (0.0-1.0)
- **Check constraints** para níveis (mínimo ≤ máximo)

## 🎯 Benefícios da Implementação

### ✅ Lógica no Banco
- **Performance otimizada**
- **Consistência garantida**
- **Fácil manutenção**
- **Regras centralizadas**

### ✅ Flexibilidade
- **Configuração por planeta**
- **Probabilidades ajustáveis**
- **Níveis dinâmicos**
- **Fácil adição de novos inimigos**

### ✅ Integridade
- **Validação automática**
- **Transações ACID**
- **Histórico completo**
- **Rollback em caso de erro**
